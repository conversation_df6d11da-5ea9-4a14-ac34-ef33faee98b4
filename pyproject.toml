[project]
name = "ducklake-speedrun"
version = "0.1.0"
description = "Duck Lake ETL utility for end-to-end data pipeline with GCS and PostgreSQL"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "duckdb>=1.3",  # Using stable version that supports ducklake
    "dlt[duckdb,sql_database]>=1.0",
    "psycopg2-binary>=2.9",
    "openai>=1.14.0",
    "google-cloud-storage>=2.10",
    "python-dotenv>=1.0",
    "sqlalchemy>=1.4",
]

[project.scripts]
ducklake-etl = "ducklake_etl:main"
events-pipeline = "events_pipeline:main"
insert-event-data = "insert_event_data:main"
query-events-json = "query_events_json:query_events_examples"
